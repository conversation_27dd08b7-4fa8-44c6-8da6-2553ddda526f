{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-multi-carousel": "^2.8.4", "react-router-dom": "^6.22.2", "react-toastify": "^10.0.4"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.1.4"}}